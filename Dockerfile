FROM registry.nscc-tj.cn/library/node:20.18.1-slim AS builder
# --max-old-space-size
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_OPTIONS=--max-old-space-size=8192
ENV TZ=Asia/Shanghai
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm
WORKDIR /ai-educationfe
COPY . /ai-educationfe
RUN pnpm install
RUN pnpm run build
# 拷贝dist
FROM registry.nscc-tj.cn/library/alpine:3.20
COPY --from=builder /ai-educationfe/dist /dist
