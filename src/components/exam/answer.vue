<template>
    <div class="answer">
        <div>
            答题卡
        </div>
        <div class="line"></div>
        <div class="answer_scroll">
            <div class="answer-item">
                <!-- :class="(questionStore.titleNumber == index+1 || item.userAnswer.length > 0&& item.userAnswer[0] != '') ? 'active' : ''"  -->
                <div v-for="(item,index) in examList" :key="index" class="common"
                    :class="getItemClass(index)"
                    @click="chooseItem(index+1)">
                    <div>{{ index+1 }}</div>
                </div>
            </div>
        </div>

        <div class="line"></div>
        <div class="answer-remark">
            <div class="flex"><div class="already"></div>已做</div>
            <div class="flex"><div class="nodo"></div>未做</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'

const props = defineProps(['examList'])

const questionStore = useQuestionStore()  //获取pinia里面的题号数据

const chooseItem = (index: number) => {
    questionStore.updateTitleNumber(index) //修改题号
}

// 动态 class 的逻辑
const getItemClass = (index: number) => {
  const item = props.examList[index];
  if(questionStore.titleNumber === index + 1){
    return 'active1';
  }else if(item.userAnswer.length > 0 && item.userAnswer[0] !== ''){
      return 'active';
  }else{
      return '';
  }
};

</script>
<style lang="scss" scoped>
.flex{
    display: flex;
    align-items: center;
}
.answer {
    width: 100%;
    padding: 30px;
    text-align: center;
    background-color: #fff;
    border-radius: 10px;
    height: 90vh;
}

.answer>div:first-child {
    font-weight: 600;
}

.line {
    height: 1px;
    background-color: #ccc;
    margin: 10px 8px;
}

.answer_scroll {
    height: 70vh;
    overflow: auto;
    scrollbar-width: none; /* 针对Firefox浏览器 */
}
.answer-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr; /* 三列等宽 */
    grid-template-rows: auto auto; /* 两行，第一行高度自适应，第二行100px */
}

.common {
    border: #ccc 1px solid;
    padding: 2px 0px;
    border-radius: 4px;
    min-width: 32px;
    margin: 6px;
    height: 32px;
    cursor: pointer;
    text-align: center;
}
.active1 {
    color: rgb(54, 170, 253);
    border: rgb(54, 170, 253) 1px solid;
}
.active {
    color: #fff;
    background-color:rgb(54, 170, 253);
}
.answer-remark{
    margin-top: 4vh;
    display: flex;
    justify-content: space-around;
}
.already{
    width:  16px;
    height: 16px;
    background-color: rgb(54, 170, 253);
    border-radius: 2px;
    margin-right: 4px;
}
.nodo{
    width:  16px;
    height: 16px;
    border: 1px solid #ccc;
    border-radius: 2px;
    margin-right: 4px;
}
</style>
