<template>
    <div class="time">
        总剩余时间
        <br>
        <span>{{ time }}</span>


        <div class="submit"><el-button type="primary" round @click="submitAnswer">交卷</el-button></div>
    </div>
</template>

<script lang="ts" setup>

import { ref, watch, onMounted, onUnmounted } from "vue";

defineProps(['examList'])
const emit = defineEmits(['submitExam'])

const time = ref('01:01:00')
let timer: any = null; // 用于存储定时器的引用

// 格式化时间函数
function formatTime(timeString: any) {
    const [hours, minutes, seconds] = timeString.split(':').map(Number);
    return { hours, minutes, seconds };
}
// 更新倒计时函数
function updateCountdown() {
    const { hours, minutes, seconds } = formatTime(time.value);
    if (seconds > 0) {
        time.value = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds - 1).padStart(2, '0')}`;
    } else if (minutes > 0) {
        time.value = `${String(hours).padStart(2, '0')}:${String(minutes - 1).padStart(2, '0')}:59`;
    } else if (hours > 0) {
        time.value = `${String(hours - 1).padStart(2, '0')}:59:59`;
    } else {
        clearInterval(timer); // 倒计时结束，清除定时器
        alert('倒计时结束'); // 可以根据需要处理结束逻辑，例如显示提示信息或重置时间等
    }
}

// 开始倒计时函数
function startCountdown() {
    timer = setInterval(updateCountdown, 1000); // 每秒更新一次时间
}

function submitAnswer() {
    emit('submitExam')
}

// 组件挂载时自动开始倒计时（可选）
onMounted(() => {
    startCountdown(); // 或者根据需要注释掉这行，改为手动开始倒计时按钮触发
});

// 组件卸载时清除定时器以避免内存泄漏（可选）
onUnmounted(() => {
    clearInterval(timer); // 确保在组件销毁前清除定时器
});


</script>
<style lang="scss" scoped>
.time {
    width: 100%;
    padding: 30px 0 0 0;
    text-align: center;
    background-color: #fff;
    border-radius: 10px;
    color: #2424248b;
    font-size: 14px;
    height: 36vh;
}

span {
    font-size: 30px;
    font-weight: bold;
    color: rgb(54, 170, 253);
}

.submit {
    margin-top: 16vh;
    line-height: 40px;
    border-radius: 30px;
    text-align: center;
    margin-left: 50%;
    transform: translateX(-50%);
}

v-deep.el-button,
.el-button.is-round {
    padding: 16px 33px;
}
</style>