<template>
    <div class="question">
        <div v-for="(item, index) in examList" :key="index">
            <div v-if="questionStore.titleNumber == index + 1">
                <div class="number">
                    <span style="font-weight: 600;">
                        {{ item.choiseTypName }}：
                    </span>
                    <!-- 根据题干信息，在答题框内输入正确答案或者上传附件。(1分) -->
                </div>

                <div class="question-title">
                    <div>
                        {{ questionStore.titleNumber }}、
                        {{ item.qsTitle }}
                    </div>
                    <div class="q_choose">
                        <Questions :item="item" @answerChange="answerChange"></Questions>
                    </div>
                </div>
            </div>
        </div>

        <div class="question-number">
            <div class="">
                <el-button type="primary" round @click="questionStore.prevTitle" v-if="questionStore.titleNumber > 1">上一题</el-button>
                <el-button type="primary" round @click="questionStore.nextTitle" v-if="questionStore.titleNumber < examList.length">下一题</el-button>
                <el-button type="primary" round @click="submitAnswer" v-if="questionStore.titleNumber == examList.length">交卷</el-button>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import Questions from './questions.vue'

const questionStore = useQuestionStore() // 获取当前题号

//题目列表
defineProps(['examList'])
const emit = defineEmits(['changeOptions','submitExam']);
// 单项选择
function answerChange(item: Object) {
	let options = {
		'answerData': item,
		'examIndex': questionStore.titleNumber - 1
	}
	emit('changeOptions', options);
}

// 提交答案
function submitAnswer() {
    console.log('提交答案')
    emit('submitExam')
}

</script>
<style lang="scss" scoped>
.question {
    width: 100%;
    // text-align: center;
    background-color: #fff;
    border-radius: 10px;
}
.question-number{
    padding: 10px 30px 50px;
}
.number {
    background-color: rgb(250, 250, 250);
    border-radius: 10px;
    padding: 10px 30px;
    font-size: 14px;
}

.question-title {
    padding: 30px 30px 16px;
    font-size: 16px;
}

.q_choose {
    margin-top: 20px;
}

v-deep.el-button,
.el-button.is-round {
    padding: 18px 23px;
}
</style>
