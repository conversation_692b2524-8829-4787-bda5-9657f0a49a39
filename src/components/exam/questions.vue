<template>
    <div class="question">
        <!-- 单选题 -->
        <div class='qc_dx' v-if="item.choiseTypeCode == 0">
            <div class="pd_item" :class="[changeAnswer(item.userAnswer, it.id)]"
                v-for="(it, index2) in item.optionsList" :key='index2' @click="radioAnswerChange(it.id)">
                <div class="pdi_left qs_index">{{ getEn(index2) }}</div>
                <div class="pdi_right">{{ it.questionOption }}</div>
            </div>
        </div>
        <!-- 多选题 -->
		<div class="qc_dx" v-else-if="item.choiseTypeCode == 1">
			<div class="pd_item" :class="[changeAnswer(item.userAnswer,it.id)]" v-for="(it,index2) in item.optionsList"
				:key='index2' @click="checkAnswerChange(it.id)">
				<div class="pdi_left qs_index">{{ getEn(index2) }}</div>
				<div class="pdi_right">{{it.questionOption}}</div>
			</div>
		</div>

        <!-- 判断题 -->
        <div class='qc_dx' v-else-if="item.choiseTypeCode == 3">
			<div class="pd_item" :class="[changeAnswer(item.userAnswer,it.id)]" v-for="(it,index2) in item.optionsList"
				:key='index2' @click="radioAnswerChange(it.id)">
				<div class="pdi_left qs_index">{{ getEn(index2) }}</div>
				<div class="pdi_right">{{it.questionOption}}</div>
			</div>
		</div>
        <!-- 问答题 -->
		<div class='qc_dx' v-else-if="item.choiseTypeCode == 4">
			<div style="border:1px solid #f0f0f0;border-radius: 8rpx;">
                    <el-input
                        v-model="item.userAnswer[0]"
                        style=""
                        :rows="4"
                        type="textarea"
                        placeholder="请输入答案..."  @input="shortAnswer(item)"
                    />
			</div>
		</div>

        <!-- 填空题 -->
		<div class='qc_dx' v-else-if="item.choiseTypeCode == 6">
			<div v-if="toArray(item.keyWords).length > 0">
				<div v-for="(it,ix) in toArray(item.keyWords)" class="completion">
					<text class="completionIndex">{{ix + 1}}. </text>
					<el-input v-model="item.userAnswer[ix]" style="height: 44px;" placeholder="请输入答案"  
                            @change="handleInput($event,item.keyWords,ix)"/>
				</div>
			</div>
		</div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const Ens = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
// 根据索引获取字母
const getEn = (index: number) => {
    return Ens[index] || ""; // 如果索引超出范围，返回空字符串
};
const props = defineProps({
    item: {
        type: Object,
        default: () => ({})
    }
})
const emit = defineEmits(['answerChange']);

// 判断选项是否被选中
const changeAnswer = (userAnswer: string | null, optionId: string) => {
    if (userAnswer && userAnswer.length > 0 && userAnswer.indexOf(optionId) != -1) {
        return 'answer_options_active'
    } else {
        return ''
    }
};

// 处理单选答案的变化
const radioAnswerChange = (optionId: string) => {
    if (props.item.userAnswer.length > 0) {
        props.item.userAnswer = new Array()
    }
    props.item.userAnswer.push(optionId)
    emit('answerChange', props.item)
};
// 多选题 or 不定项
function checkAnswerChange(optionId: string) {
	let index = props.item.userAnswer.findIndex((it: string) => it == optionId)
	if (index != -1) {
		props.item.userAnswer.splice(index, 1)
	} else {
		props.item.userAnswer.push(optionId)
	}
	emit('answerChange', props.item);
}
// 问答题 
function shortAnswer(e: any) {
	emit('answerChange', e);
}

// 填空题
function handleInput(event : any, keywords : any, i: number) {
	let length = toArray(keywords).length
	for (var k = 0; k < length; k++) {
		if (i == k) {
            props.item.userAnswer[k] = event;
		} else if (i != k && props.item.userAnswer[k] == undefined) {
            props.item.userAnswer[k] = '';
		}
	}
	emit('answerChange', props.item);
}

// 字符串转数组
function toArray(array: string | null) {
	if (array != null && array != '[]') {
		let temp = array.split(',')
		temp = temp.map(item => item.replace(/\[|]/g, '').replace(/\"|"/g, '').replace(/\s/g, ""))
		return temp
	} else {
		return new Array(0)
	}
}

</script>
<style lang="scss" scoped>
.qc_dx {
    width: 100%;

    // margin-top: 24px;
    // padding-bottom: 24px;
    .confirmChange {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;
    }

    .completion {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 30px;
        width: 80%
    }

    .completionIndex {
        font-size: 18px;
        width: 30px;
    }


    .pd_item {
        // width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: row;
        padding: 2px 3%;
        box-sizing: border-box;
        background: #F5F7FB;
        border-radius: 6px;
        margin-bottom: 20px;
        cursor: pointer;

        .pdi_left {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 1px solid #d1d1d1;
            background-color: #fff;
            text-align: center;
            line-height: 30px;
            font-size: 16px;
        }

        .pdi_right {
            width: 90%;
            padding: 10px 20px;
            font-size: 16px;
            letter-spacing: 1px;
        }
    }
    .pd_item:hover {
        border: 1px solid #2F97FF;
    }

    .answer_options_active {
        color: #2F97FF;
        background: #ECF4FF;

        .qs_index {
            color: #2F97FF;
            border: 1px solid #2F97FF !important;
        }
    }

    .answer_options_error {
        color: #FF5054;
        background: #FFE7E7;

        .qs_index {
            color: #FF5054;
            border: 1px solid #FF5054 !important;
        }
    }
}
v-deep.el-input__wrapper{
    height: 40px;
}
</style>