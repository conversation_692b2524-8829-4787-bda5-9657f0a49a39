<script lang="ts">
  export interface FormValues {
    name: string
  }
</script>

<script setup lang="ts">
  import type { FormProps } from 'ant-design-vue'

  const props = defineProps<FormProps>()

  const formValues = ref<Partial<FormValues>>({})
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput
        v-model:value="formValues.name"
        placeholder="请输入知识库名称"
      />
    </AFormItem>
  </AForm>
</template>
