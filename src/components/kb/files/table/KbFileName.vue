<script setup lang="ts">
  import { createMachine, assign } from 'xstate'
  import { useMachine } from '@xstate/vue'

  import TextEllipsis from '@/components/ui/TextEllipsis.vue'

  import IconEdit from '~icons/local/pencil-square'

  type Props = {
    fileName: string
  }

  const props = defineProps<Props>()

  const fileNameMachine = createMachine({
    id: 'kb-file-name',
    context: {
      committedValue: '',
      value: '',
    },
    initial: 'reading',
    states: {
      reading: {
        on: { edit: 'editing' },
      },
      editing: {
        on: {
          change: {
            actions: assign({
              value: ({ event }) => event.value,
            }),
          },
          commit: {
            actions: assign({
              committedValue: ({ context }) => context.value,
            }),
            target: 'reading',
          },
          cancel: {
            actions: assign({
              value: ({ context }) => context.committedValue,
            }),
            target: 'reading',
          },
        },
      },
    },
  })

  const { send, snapshot, actorRef } = useMachine(fileNameMachine)

  actorRef.subscribe(async (snapshot) => {
    if (snapshot.value === 'editing') {
      await nextTick()
      getInputElement()?.focus()
      return
    }
    await nextTick()
    getInputElement()?.blur()
  })

  const rootRef = useTemplateRef('rootRef')
  const isHovered = useElementHover(rootRef)

  const inputRef = useTemplateRef('inputRef')
  function getInputElement() {
    if (!inputRef.value) {
      return null
    }
    return inputRef.value.$el as HTMLInputElement
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault()
      send({ type: 'commit' })
    } else if (event.key === 'Escape') {
      event.preventDefault()
      getInputElement()?.blur()
    }
  }

  function handleBlur() {
    nextTick(() => send({ type: 'cancel' }))
  }
</script>

<template>
  <div
    class="-m-4 flex h-6 items-center p-4"
    ref="rootRef"
  >
    <FileTypeImg
      :file-name="props.fileName"
      class="mr-3 w-5"
    />

    <div
      v-if="snapshot.value === 'reading'"
      class="min-w-0 flex-1"
    >
      <TextEllipsis
        :text="fileName"
        :lines="1"
        :tooltip="{
          title: fileName,
          placement: 'top',
        }"
      />
    </div>

    <!-- 编辑状态 -->
    <div
      v-else
      class="min-w-0 flex-1"
    >
      <AInput
        :value="snapshot.context.value"
        @update:value="(value) => send({ type: 'change', value })"
        size="small"
        @keydown="handleKeydown"
        @blur="handleBlur"
        ref="inputRef"
      />
    </div>

    <!-- 图标按钮 -->
    <AButton
      type="text"
      size="small"
      class="ml-1 size-auto! shrink-0 p-1!"
      @click="send({ type: 'edit' })"
      v-if="snapshot.value === 'reading' && isHovered"
    >
      <IconEdit
        v-if="snapshot.value === 'reading'"
        class="text-primary"
      />
    </AButton>
  </div>
</template>
