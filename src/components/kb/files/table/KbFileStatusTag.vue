<script lang="ts">
  export type KbFileStatus = 'complete' | 'running' | 'failed' | 'not-started'

  type Props = {
    status: KbFileStatus
  }

  const statusMap: Record<KbFileStatus, { text: string; color: string }> = {
    complete: { text: '已完成', color: 'success' },
    running: { text: '解析中', color: 'processing' },
    failed: { text: '解析失败', color: 'error' },
    'not-started': { text: '未开始', color: 'default' },
  }
</script>

<script setup lang="ts">
  const props = defineProps<Props>()
</script>

<template>
  <ATag
    :color="statusMap[props.status].color"
    class="flex! w-16 items-center justify-center"
  >
    {{ statusMap[props.status].text }}
  </ATag>
</template>
