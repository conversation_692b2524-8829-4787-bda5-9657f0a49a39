<script lang="ts">
  export type Props = {
    defaultValues: {
      name: string
      description?: string
      embeddingModel?: string
      courses?: string[]
    }
  }
</script>

<script setup lang="ts">
  const props = defineProps<Props>()

  const name = ref<string>(props.defaultValues.name)
  const description = ref<string | undefined>(props.defaultValues.description)
  const embeddingModel = ref<string | undefined>(props.defaultValues.embeddingModel)
  const courses = ref<string[] | undefined>(props.defaultValues.courses)
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">基本设置</div>

    <AFormItem
      required
      name="name"
      label="知识库名称"
    >
      <AInput v-model:value="name" />
    </AFormItem>

    <AFormItem
      name="description"
      label="知识库描述"
    >
      <ATextarea v-model:value="description"  />
    </AFormItem>

    <AFormItem
      name="embeddingModel"
      label="嵌入模型"
      required
    >
      <ASelect v-model:value="embeddingModel">
        <ASelectOption value="1">模型1</ASelectOption>
        <ASelectOption value="2">模型2</ASelectOption>
      </ASelect>
    </AFormItem>

    <AppPermissionGuard :roles="['teacher']">
      <AFormItem
        name="courses"
        label="关联课程"
      >
        <ASelect
          v-model:value="courses"
          mode="multiple"
        >
          <ASelectOption value="1">课程1</ASelectOption>
          <ASelectOption value="2">课程2</ASelectOption>
        </ASelect>
      </AFormItem>
    </AppPermissionGuard>
  </div>
</template>
