<script lang="ts">
  export type Props = {
    defaultValues: {
      chunkMethod: string
      separator: string
      keywordNum: number
      questionNum: number
    }
  }
</script>

<script setup lang="ts">
  const props = defineProps<Props>()

  const chunkMethod = ref<string>(props.defaultValues.chunkMethod)
  const separator = ref<string>(props.defaultValues.separator)
  const keywordNum = ref<number>(props.defaultValues.keywordNum)
  const questionNum = ref<number>(props.defaultValues.questionNum)
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">分块参数设置</div>

    <AFormItem
      required
      name="chunkMethod"
      label="分块方法"
    >
      <ASelect v-model:value="chunkMethod">
        <ASelectOption value="1">方法1</ASelectOption>
        <ASelectOption value="2">方法2</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      name="separator"
      label="分隔符"
      required
    >
      <AInput v-model:value="separator" />
    </AFormItem>

    <AFormItem
      name="keywordNum"
      label="关键词数量"
      required
    >
      <AInputNumber v-model:value="keywordNum" />
    </AFormItem>

    <AFormItem
      name="questionNum"
      label="问题数量"
      required
    >
      <AInputNumber v-model:value="questionNum" />
    </AFormItem>
  </div>
</template>
