<template>
  <div class="outline-timeline">
    <div 
      v-for="(item, index) in items" 
      :key="index" 
      class="timeline-item"
    >
      <div class="timeline-label">{{ item.label }}</div>
      <div class="timeline-marker">
        <div class="timeline-dot"></div>
        <div class="timeline-horizontal-line"></div>

      </div>
      <div class="timeline-content">
        <div class="content-box">{{ item.content }}</div>
      </div>

      <!-- 垂直连接线只在点与点之间显示 -->
      <div 
        v-if="index < items.length - 1" 
        class="timeline-vertical-line"
      ></div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

// 明确定义TimelineItem接口
interface TimelineItem {
  label: string;
  content: string;
  [key: string]: any; // 允许其他可能的属性
}
// 使用接口定义props类型
defineProps<{
  items: TimelineItem[]
}>();

</script>

<style scoped>
.outline-timeline {
  position: relative;
  padding: 0.5vw;
}


.timeline-item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1vw;
}

.timeline-label {
  width: 5vw;
  text-align: right;
  padding-right:2vw;
  font-size: 1vw;
  font-weight: bold;
  color: #333;
}

.timeline-marker {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 2;
}

.timeline-dot {
  width: 0.8vw;
  height: 0.8vw;
  border-radius: 50%;
  background-color: #409eff;
  z-index: 3;
}

.timeline-horizontal-line {
  height: 0.1vh;
  width: 1vw;
  background-color: #e0e0e0;
}

.timeline-content {
  flex: 1;
}

.content-box {
  border: 1px solid #eaeaea;
  border-radius: 94px;
  padding: 10px 15px;
  color: #333;
}

/* 垂直连接线 - 现在作为每个时间线项目的一部分 */
.timeline-vertical-line {
  position: absolute;
  top: 50%;
  bottom: 0;
  left: 5.4vw; /* 调整左边距以对齐蓝点 */
  width: 0.1vh;
  height: 150%;
  background-color: #e0e0e0;
  z-index: 1;
}

</style>
