<script lang="ts">
  export interface FormValues {
    name: string
    source: string
    framework: string
    replicaNum: number
    modelType: string
    vcardNum?: number
    parameters?: string[]
  }

  const VCARD_NUM_PARAMETER = '--tensor_parallel_size'

  function rawDataToFormValues(rawModel: Partial<API.Models.Model>) {
    const [vcardNum, parameters] = (function () {
      const index = rawModel.backend_parameters?.findIndex((parameter) =>
        parameter.startsWith(VCARD_NUM_PARAMETER),
      )

      if (index === undefined || index < 0) {
        return [1, rawModel.backend_parameters]
      }

      const vcardNum = Number(rawModel.backend_parameters![index].split('=')[1])
      const parameters = rawModel.backend_parameters!.filter((_, i) => i !== index)

      return [vcardNum, parameters]
    })()

    return {
      name: rawModel.name,
      source: rawModel.local_path!,
      framework: rawModel.backend,
      replicaNum: rawModel.replicas,
      modelType: rawModel.categories?.[0],
      vcardNum,
      parameters,
    }
  }

  function formValuesToRawData(formValues: Partial<FormValues>): Partial<API.Models.Model> {
    const backend_parameters = (function () {
      const index = formValues.parameters?.findIndex((parameter) =>
        parameter.startsWith(VCARD_NUM_PARAMETER),
      )

      if (index === undefined || index < 0) {
        return [...(formValues.parameters ?? []), `${VCARD_NUM_PARAMETER}=${formValues.vcardNum}`]
      }

      formValues.parameters![index] = `${VCARD_NUM_PARAMETER}=${formValues.vcardNum}`

      return formValues.parameters
    })()

    return {
      name: formValues.name,
      source: 'local_path',
      local_path: formValues.source,
      backend: formValues.framework,
      replicas: formValues.replicaNum,
      categories: formValues.modelType ? [formValues.modelType] : undefined,
      backend_parameters,
    }
  }
</script>

<script setup lang="ts">
  import type { FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import { injectModelsContext } from '@/utils/context/models'
  import { message } from 'ant-design-vue'

  import IconHelp from '~icons/lucide/circle-help'
  import IconPlus from '~icons/lucide/plus'
  import IconMinus from '~icons/lucide/minus'

  const props = defineProps<FormProps>()

  const { deployFormRawModel, formAction } = injectModelsContext()

  const formValues = ref<Partial<FormValues> & { parameters: string[] }>({
    parameters: [],
  })

  const initialValues = computed(() => rawDataToFormValues(deployFormRawModel.value))
  watchEffect(() => {
    formValues.value = {
      ...initialValues.value,
      parameters: initialValues.value.parameters ?? [],
    }
  })
  watchEffect(() => {
    if (!formValues.value.framework) {
      formValues.value.framework = 'vllm'
    }
  })
  watchEffect(() => {
    if (!formValues.value.modelType) {
      formValues.value.modelType = 'llm'
    }
  })

  const queryClient = useQueryClient()
  const { mutate, status, error } = useMutation({
    mutationFn() {
      if (formAction.value === 'create') {
        return modelsFetcher('/models/', {
          method: 'post',
          body: formValuesToRawData(formValues.value),
        })
      } else {
        return modelsFetcher(`/models/${deployFormRawModel.value.id}/`, {
          method: 'put',
          body: {
            ...initialValues.value,
            ...formValuesToRawData(formValues.value),
          },
        })
      }
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: modelsQueryKey.modelList() })
    },
  })

  watchEffect(() => {
    if (error.value) {
      console.log('error changes')
      message.error(error.value.message)
    }
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => {
    emit('update:status', status.value)
  })
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    layout="vertical"
    class="space-y-4!"
    @finish="mutate"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput v-model:value="formValues.name" />
    </AFormItem>

    <AFormItem
      label="模型路径"
      name="source"
      required
    >
      <AInput v-model:value="formValues.source" />

      <template #tooltip>
        <ATooltip>
          <IconHelp class="ml-1 size-3.5 cursor-help" />
          <template #title>
            <ol className="m-0! list-decimal space-y-2 pl-5 text-xs">
              <li>
                GGUF 格式模型：
                <br />
                指定模型文件，例如: /usr/local/models/model.gguf
              </li>
              <li>
                分片 GGUF 格式模型：
                <br />
                指定模型的第一个分片文件，例如: /usr/local/models/model-00001-of-00004.gguf
              </li>
              <li>
                Safetensors 格式模型：
                <br />
                指定包含 .safetensors 和 config.json 文件的模型目录，例如: /usr/local/models/model/
              </li>
            </ol>
          </template>
        </ATooltip>
      </template>
    </AFormItem>

    <AFormItem
      label="加速框架"
      name="framework"
      required
    >
      <AInput
        v-model:value="formValues.framework"
        disabled
      />
    </AFormItem>

    <AFormItem
      label="副本数"
      name="replicaNum"
      required
    >
      <AInputNumber
        v-model:value="formValues.replicaNum"
        :min="0"
        :max="100"
        class="w-full!"
      />
    </AFormItem>

    <AFormItem
      label="模型类别"
      name="modelType"
      required
    >
      <ASelect
        default-value="llm"
        v-model:value="formValues.modelType"
      >
        <ASelectOption value="llm">LLM</ASelectOption>
        <ASelectOption value="embedding">Embedding</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="显卡张数"
      name="vcardNum"
      required
    >
      <AInputNumber
        v-model:value="formValues.vcardNum"
        class="w-full!"
        :min="0"
        :max="100"
      />
    </AFormItem>

    <div>
      <div class="flex items-center pb-2">
        <div>后端参数</div>
        <ATooltip>
          <IconHelp class="ml-1 size-3.5 cursor-help" />
          <template #title>
            <span class="text-xs">
              vLLM 参数请参考
              <a
                href="https://docs.vllm.ai/en/stable/serving/openai_compatible_server.html#cli-reference"
                target="_blank"
                rel="noreferrer"
              >
                文档
              </a>
            </span>
          </template>
        </ATooltip>
      </div>

      <div class="space-y-4 rounded-md border p-5">
        <template v-if="formValues.parameters.length > 0">
          <div
            class="flex items-start gap-2.5"
            v-for="(parameter, index) in formValues.parameters"
            :key="index + parameter"
          >
            <AFormItem
              class="m-0! w-full!"
              :name="['parameter', index]"
              :rules="[{ required: true, message: '请输入参数' }]"
            >
              <AInput
                autofocus
                v-model:value="formValues.parameters[index]"
              />
            </AFormItem>

            <AButton
              shape="circle"
              size="small"
              class="mt-1 flex! items-center justify-center p-0!"
              @click="formValues.parameters.splice(index, 1)"
              html-type="button"
            >
              <IconMinus />
            </AButton>
          </div>
        </template>

        <div class="flex items-center gap-2.5">
          <button
            class="flex w-full cursor-pointer items-center justify-center gap-2 rounded-md bg-[#f0f0f0] p-2 transition-colors hover:bg-[#e0e0e0]"
            @click.prevent="formValues.parameters.push('')"
            type="button"
          >
            <IconPlus />
            添加参数
          </button>

          <div
            class="w-6"
            v-if="formValues.parameters.length > 0"
          />
        </div>
      </div>
    </div>
  </AForm>
</template>
