<script setup lang="ts">
  import { injectModelsContext } from '@/utils/context/models'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawModels } = injectModelsContext()

  const rawModel = computed(() => rawModels.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: deleteModel, status } = useMutation({
    async mutationFn() {
      return modelsFetcher(`/models/${rawModel.value.id}/`, {
        method: 'delete',
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: modelsQueryKey.modelList() })
    },
  })
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="删除"
    @click="deleteModel()"
  >
    <IconLoading
      v-if="status === 'pending'"
      class="animate-spin"
    />
    <IconTrash v-else />
  </AButton>
</template>
