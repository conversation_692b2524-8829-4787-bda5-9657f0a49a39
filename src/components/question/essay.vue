<template>
    <div class="">
        <div>
            <div class="group-title">
                问答题 - 试题题干
            </div>
        </div>
        <div class="options-item">
            <div class="aiEditorClass" v-if="params.text || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div v-else>
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>
        <div>
            <div class="group-title" style="margin-top: 40px;">
                参考答案
            </div>
        </div>
        <div class="options-item">
            <div class="aiEditorClass" v-if="params.answer || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.answer" :indexNumber="'answer'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div v-else>
                <QuestionEditor :questionValue="params.answer" :indexNumber="'answer'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>

        <el-radio-group v-model="params.score_mode" style="margin-top: 44px;">
            <el-radio value="human" size="large" >人工判分</el-radio>
            <el-radio value="keywords" size="large" >按关键字自动判分</el-radio>
            <el-radio value="ai" size="large" >AI阅卷判分</el-radio>
        </el-radio-group>

        <div>
            <div style="margin: 10px 0;"> 判分点 </div>
            <div v-for="(item, index) in params.judge_point" :key="index">
                <div class="options-item" style="margin: 0 0 10px 0 ;">
                    <div style="flex: 1;" class="options-item ">
                        <el-input style="height: 40px;"  placeholder="请输入判分内容" v-model="item.content" />
                        <el-input-number v-model="item.score"  :min="1" controls-position="right"  size="large" style="margin-left: 20px;width: 160px;" >
                            <template #suffix>
                                <span>分</span>
                            </template>
                        </el-input-number>
                    </div>
                    <div style="margin: 0 0 0 10px;cursor: pointer;">
                        <el-icon :size="20" color="#ff0000" @click="deleteQuestion(index)">
                            <Delete v-show="params.judge_point.length > 1" />
                        </el-icon>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <el-button type="primary" @click="addQuestion" size="large" style="margin-top: 14px;">添加判分</el-button>
        </div>

        

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                可获得分数
            </div>
        </div>
        <div class="options-item" style="margin: 0 30px 0 0 ;">
            <el-input-number v-model="params.total_score"  :min="1" controls-position="right"  style="width: 100%;"/>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题知识点
            </div>
        </div>
        <div class="options-item">
            <el-input v-model="params.knowledge_point" style="width:400px" :rows="4" type="textarea"
                placeholder="请输入试题知识点" />
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题难度
            </div>
        </div>
        <div class="options-item">
            <el-select v-model="params.difficulty" placeholder="请选择试题难度" size="large" style="width: 240px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题解析
            </div>
        </div>
        <div class="options-item">
            <div v-if="params.explanation || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div v-else>
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题标签
            </div>
        </div>
        <div class="options-item" style="align-items: flex-end;">
            <div style="" class="lable">
                标签1
            </div>
            <div style="margin: 0 30px 0 30px;">
                <el-button size="large" type="primary" plain>添加标签</el-button>
            </div>
        </div>

        <el-button type="primary" @click="saveQuestion" size="large" :loading="loadingButton" style="margin: 54px 0;">保存试题</el-button>

    </div>
</template>
<script lang="ts" setup>
import QuestionEditor from "@/components/QuestionEditor.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { addAnswerQuestions, editAnswerQuestions } from '@/services/api/question'
import { openMessage,getEn } from '@/utils/util'
import { useRouter } from 'vue-router';

const loadingButton = ref(false);

const router = useRouter();
// 定义数据类型
interface EditorData {
    indexNumber: string | number;
    valueEditor: string;
}

const props = defineProps({
    questionDetails: {
        type: Object,
        required: true
    },
    isType: {
        tye: String,
        required: false
    }
})

const options = [
    {
        value: 0,
        label: '简单',
    },
    {
        value: 1,
        label: '中等',
    },
    {
        value: 2,
        label: '困难',
    }
]


// 默认值
const defaultParams = {
    text: "",
    difficulty: 0,  // 难度
    knowledge_point: "",  // 知识点
    label_id: 1,  // 标签
    answer: "", //答案，
    judge_point:[
        {
            "content":"", //判分内容
            "score":1 //分数，若不设置该字段数值，系统不会设置默认分数
        }
    ], // 判分点，"content":判分内容, "score":该判分内容的分值
    explanation: '', // 解析
    score_mode:"human",  //得分设置，("human", "人工判分"), ("keywords", "按关键字自动判分"), ('ai', 'AI阅卷判分')，默认是human
    total_score:1  //可获得分数
};

// 响应式对象
const params = ref({ ...defaultParams });

onMounted(() => {
    if (props.questionDetails) {
        params.value = { ...defaultParams, ...props.questionDetails }; // 深度合并默认值和传入的值
    }
})


function addQuestion() {
    params.value.judge_point.push({ content: "", score: 1 })
}
//获取题干文本数据
function getText(data: EditorData) {
    if (data.indexNumber == 'text') {
        params.value.text = data.valueEditor
    } else if (typeof data.indexNumber == 'number') {
        // params.value.answer[data.indexNumber].blank = `${data.valueEditor}`// ${getEn(data.indexNumber)}.${' '}
    } else if (data.indexNumber == 'explanation') {
        params.value.explanation = data.valueEditor
    } else if (data.indexNumber == 'answer') {
        params.value.answer = data.valueEditor
    }
}

//删除选项
function deleteQuestion(index: number) {
    params.value.judge_point.splice(index, 1);
}



//保存
function saveQuestion() {
    
    // const optionsToSend = params.value.options.map((option,index) => `${getEn(index)}.${' '}${option.value}`);
    const hasEmptyOption = params.value.judge_point.some(option => option.content.trim() === '');
    

    if (params.value.text == '') {
        openMessage('请输入题干', 'error')
        return
    }
    if (hasEmptyOption) {
        openMessage('请输入全部判分内容', 'error');
        return;
    }
    if (params.value.answer == '') {
        openMessage('请输入答案', 'error')
        return
    }
    console.log(params.value, 'params.value');
    loadingButton.value = true;

    // return
    if(props.isType == 'edit'){
        console.log('修改试题')
        editAnswerQuestions(params.value).then(res => {
            console.log(res);
            router.go(-1);
            loadingButton.value = false;
            // openMessage('修改成功', 'success')
            // setTimeout(() => {
            //     resetForm();
            //     window.location.reload(); 
            // },1000)   // 刷新页面
        })
    }else if(props.isType == 'add'){
        console.log('新增试题')
        addAnswerQuestions(params.value).then(res => {
            // console.log(res, 'res');
            openMessage('保存成功', 'success')
            loadingButton.value = false;
            setTimeout(() => {
                resetForm();
            },1000)   // 刷新页面
        })
    }

}
function resetForm() {
    params.value.text = ''
    params.value.difficulty = 0
    params.value.knowledge_point = ''
    params.value.label_id = 1
    params.value.answer = "",
    params.value.judge_point =[
        {
            "content":"", 
            "score":1 
        }
    ],

    params.value.explanation = '',
    params.value.score_mode = "human",  //得分设置，("partial", "按空得分累加"), ("all", "全部正确得分")，默认是partial
    params.value.total_score = 1 //可获得分数
}


</script>
<style lang="scss" scoped>
@import url('@/assets/css/questioncss/selectCss.css');

::v-deep(.el-input-number .el-input__inner){
    text-align: left !important;
}
</style>