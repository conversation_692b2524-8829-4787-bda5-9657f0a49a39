<template>
    <div class="">
        <div>
            <div class="group-title">
                填空题 - 试题题干
            </div>
        </div>
        <div class="options-item">
            <div class="aiEditorClass" v-if="params.text || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div class="aiEditorClass" v-else>
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>
        <div class="group-title" style="margin-top: 24px;">
            要填的空和正确答案
        </div>
        <div class="extra">
            下面列出了填空的个数，您需要在填空上写上正确答案。如果您的题含有不止一个空，您可以点击下面的按钮增加更多的空。<br>
            如果您的一个空可以有<span style="font-weight: bold;">多个正确答案</span>, 正确答案请用竖线 | 分开，例如：大象|象<br>
        </div>
        <div>
            <div v-for="(item, index) in params.answer" :key="index">
                <div style="margin: 10px 0;"> （{{ getEn(index) }}）</div>
                <div class="options-item">
                    <div style="flex: 1;" class="options-item ">
                        <el-input style="height: 40px;" placeholder="在这里写上该空的答案，如果有多个正确答案，请以竖线（|）分开" v-model="item.blank" />
                        <el-input-number v-model="item.score" :min="1" controls-position="right"  size="large" style="margin-left: 20px;width: 160px;" >
                            <template #suffix>
                                <span>分</span>
                            </template>
                        </el-input-number>
                    </div>
                    <div style="margin: 0 0 0 10px;cursor: pointer;">
                        <el-icon :size="20" color="#ff0000" @click="deleteQuestion(index)">
                            <Delete v-show="params.answer.length > 1" />
                        </el-icon>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <el-button type="primary" @click="addQuestion" size="large" style="margin-top: 24px;">新增填空</el-button>
        </div>

        <el-radio-group v-model="params.score_mode" style="margin-top: 44px;">
            <el-radio value="partial" size="large" >按空得分累加</el-radio>
            <el-radio value="all" size="large" >全部正确得分</el-radio>
        </el-radio-group>

        <div style="margin-top: 14px;">
            <div class="group-titlef">
                可获得分数
            </div>
        </div>
        <div class="options-item" style="margin: 0 30px 0 0 ;">
            <el-input v-model="params.total_score"  />
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题知识点
            </div>
        </div>
        <div class="options-item">
            <el-input v-model="params.knowledge_point" style="width:400px" :rows="4" type="textarea"
                placeholder="请输入试题知识点" />
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题难度
            </div>
        </div>
        <div class="options-item">
            <el-select v-model="params.difficulty" placeholder="请选择试题难度" size="large" style="width: 240px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题解析
            </div>
        </div>
        <div class="options-item">
            <div v-if="params.explanation || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div v-else>
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题标签
            </div>
        </div>
        <div class="options-item" style="align-items: flex-end;">
            <div style="" class="lable">
                标签1
            </div>
            <div style="margin: 0 30px 0 30px;">
                <el-button size="large" type="primary" plain>添加标签</el-button>
            </div>
        </div>

        <el-button type="primary" @click="saveQuestion" size="large" :loading="loadingButton" style="margin: 54px 0;">保存试题</el-button>

    </div>
</template>
<script lang="ts" setup>
import QuestionEditor from "@/components/QuestionEditor.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { addFillQuestions, editFillQuestions } from '@/services/api/question'
import { openMessage,getEn } from '@/utils/util'
import { useRouter } from 'vue-router';

const loadingButton = ref(false);

const router = useRouter();
// 定义数据类型
interface EditorData {
    indexNumber: string | number;
    valueEditor: string;
}

const props = defineProps({
    questionDetails: {
        type: Object,
        required: true
    },
    isType: {
        tye: String,
        required: false
    }
})

const options = [
    {
        value: 0,
        label: '简单',
    },
    {
        value: 1,
        label: '中等',
    },
    {
        value: 2,
        label: '困难',
    }
]


// 默认值
const defaultParams = {
    text: "",
    difficulty: 0,  // 难度
    knowledge_point: "",  // 知识点
    label_id: 1,  // 标签
    // options: [
    //     { value: "", key: uuidv4() },
    // ],
    answer: [
        {
            "blank":"",
            "score":1
        },
    ], //答案，"blank":答案内容, "score":该空的分数，如不设置默认为1分
    explanation: '', // 解析
    score_mode:"partial",  //得分设置，("partial", "按空得分累加"), ("all", "全部正确得分")，默认是partial
    total_score:''  //可获得分数
};

// 响应式对象
const params = ref({ ...defaultParams });

onMounted(() => {
    if (props.questionDetails) {
        params.value = { ...defaultParams, ...props.questionDetails }; // 深度合并默认值和传入的值
    }
})


function addQuestion() {
    params.value.answer.push({ blank: "", score: 1 })
}
//获取题干文本数据
function getText(data: EditorData) {
    if (data.indexNumber == 'text') {
        params.value.text = data.valueEditor
    } else if (typeof data.indexNumber == 'number') {
        params.value.answer[data.indexNumber].blank = `${data.valueEditor}`// ${getEn(data.indexNumber)}.${' '}
    } else if (data.indexNumber == 'explanation') {
        params.value.explanation = data.valueEditor
    }
}

//删除选项
function deleteQuestion(index: number) {
    params.value.answer.splice(index, 1);
}



//保存
function saveQuestion() {
    // const optionsToSend = params.value.options.map((option,index) => `${getEn(index)}.${' '}${option.value}`);
    const hasEmptyOption = params.value.answer.some(option => option.blank.trim() === '');
    

    if (params.value.text == '') {
        openMessage('请输入题干', 'error')
        return
    }
    if (hasEmptyOption) {
        openMessage('请输入全部填空答案', 'error');
        return;
    }
    // if (params.value.answer == '') {
    //     openMessage('请选择答案', 'error')
    //     return
    // }
    console.log(params.value, 'params.value');
    loadingButton.value = true;


    if(props.isType == 'edit'){
        console.log('修改试题')
        editFillQuestions(params.value).then(res => {
            console.log(res);
            router.go(-1);
            loadingButton.value = false;
            // openMessage('修改成功', 'success')
            // setTimeout(() => {
            //     resetForm();
            //     window.location.reload(); 
            // },1000)   // 刷新页面
        })
    }else if(props.isType == 'add'){
        console.log('新增试题')
        addFillQuestions(params.value).then(res => {
            // console.log(res, 'res');
            openMessage('保存成功', 'success')
            loadingButton.value = false;
            setTimeout(() => {
                resetForm();
                // window.location.reload(); 
            },1000)   // 刷新页面
        })
    }

}
function resetForm() {
    params.value.text = ''
    params.value.difficulty = 0
    params.value.knowledge_point = ''
    params.value.label_id = 1
    params.value.answer = [{
            "blank":"",
            "score":1   
        }
    ]
    params.value.explanation = '',
    params.value.score_mode = "partial",  //得分设置，("partial", "按空得分累加"), ("all", "全部正确得分")，默认是partial
    params.value.total_score = '' //可获得分数
}


</script>
<style lang="scss" scoped>
@import url('@/assets/css/questioncss/selectCss.css');
</style>