<template>
    <div class="back-btn" @click="() => $router.push(url)">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
    </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps({
    url: {
        type: String,
        default: ''
    }
})

</script>
<style scoped lang="scss">
.back-btn {

    cursor: pointer;
    width: 60px;
    padding-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
}
</style>