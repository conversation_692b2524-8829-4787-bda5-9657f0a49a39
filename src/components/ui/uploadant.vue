<template>
    <div class="clearfix" style="display: flex;align-items: center;">
        <div>
            <a-upload v-model:file-list="fileList" list-type="picture-card" accept=".png,.jpg,.jpeg" :multiple="false"
                @preview="handlePreview" @remove="handleRemove" :before-upload="beforeUpload">
                <div v-if="fileList.length < 1" class="ant-upload-text">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传图片</div>
                </div>
            </a-upload>
        </div>
        <div style="width: 160px;height: 53px;display: flex;font-size: 12px;line-height: 17.38px;">
            <div style="margin-right: 9px;margin-top: 2px;">
                <el-icon size="14">
                    <WarningFilled />
                </el-icon>
            </div>
            <div>
                支持jpg/jpeg/png格式单张图片不超过4M
                ，课程封面图最佳尺寸336*180
            </div>
        </div>
        <a-modal :open="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
            <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { PlusOutlined } from '@ant-design/icons-vue';
import { ref } from 'vue';
import type { UploadProps } from 'ant-design-vue';

function getBase64(file: File) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// const fileList = ref<UploadProps['fileList']>([]);
const fileList = ref([]);
const handleRemove: UploadProps['onRemove'] = file => {
    fileList.value = [];
    emits('deleteFile', null)
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
    //   console.log(file,'上传文件获取文件');
    emits('beforeUploadgetfile', file)
    return false;
};
const emits = defineEmits(['beforeUploadgetfile', 'deleteFile'])


const handleCancel = () => {
    previewVisible.value = false;
    previewTitle.value = '';
};
// const handlePreview = async (file: UploadProps['fileList'][number]) => {
const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};
</script>
<style scoped lang="scss">
:deep(.ant-upload-select-picture-card) {
    width: 336px !important;
    height: 180px !important;
    background-color: #fff !important;
}

:deep(.ant-upload-list-item-container) {
    width: 336px !important;
    height: 180px !important;
    background-color: #fff !important;
}


/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;

    /* background-color: #fff; */
    /* width: 336px;
    height: 180px; */
}
</style>
