import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal} from 'ant-design-vue';
/**
 * 显示删除确认对话框
 * @param content - 弹窗内容
 * @returns Promise<boolean> - 用户点击确认返回 true，取消或关闭返回 false
 */
export function showDeleteConfirm(content: string = '确定删除本记录吗？'): Promise<boolean> {
  return new Promise((resolve) => {
    Modal.confirm({
      icon: createVNode(ExclamationCircleOutlined),
      content,
      okText: '删除',
      okType: 'danger',
      maskClosable: true,
      bodyStyle: { padding: '0px 6px' },
      centered: true,
      onOk() {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}