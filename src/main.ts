import './assets/css/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedState from 'pinia-plugin-persistedstate'
import { VueQueryPlugin as tanstackQuery } from '@tanstack/vue-query'

import App from './App.vue'
import { router } from './router'

import ElementPlus from 'element-plus'
import Vue3Lottie from 'vue3-lottie'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
//引用图标库
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
pinia.use(piniaPluginPersistedState)

// prettier-ignore
app
  .use(pinia)
  .use(router)
  .use(tanstackQuery, vueQueryPluginOptions)
  .use(ElementPlus)
  .use(Vue3Lottie)

async function enableMocking() {
  if (!import.meta.env.VITE_ENABLE_MOCKING) {
    return
  }

  const { worker } = await import('./mocks')
  return worker.start({
    onUnhandledRequest: 'bypass',
  })
}

enableMocking().then(() => app.mount('#app'))
