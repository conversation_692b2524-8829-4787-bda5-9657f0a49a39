import { http, HttpResponse } from 'msw'
import { faker } from '@faker-js/faker'

// 生成100条模拟数据
const generateMockKbData = (): API.Kb.Kb[] => {
  return Array.from({ length: 100 }, () => {
    const id = faker.string.uuid()
    const createTime = faker.date.past({ years: 2 }).getTime()
    const updateTime = faker.date
      .between({
        from: new Date(createTime),
        to: new Date(),
      })
      .getTime()

    return {
      id,
      name: faker.company.name() + '知识库',
      description: faker.lorem.sentence(),
      chunk_num: faker.number.int({ min: 10, max: 1000 }),
      doc_num: faker.number.int({ min: 1, max: 50 }),
      token_num: faker.number.int({ min: 1000, max: 100000 }),
      create_date: new Date(createTime).toISOString().split('T')[0],
      create_time: createTime,
      update_date: new Date(updateTime).toISOString().split('T')[0],
      update_time: updateTime,
      created_by: faker.person.fullName(),
      language: faker.helpers.arrayElement(['zh', 'en', 'zh-CN']),
      pagerank: faker.number.float({ min: 0, max: 1, fractionDigits: 3 }),
      parser_id: faker.helpers.arrayElement(['text-splitter', 'naive', 'manual']),
      permission: faker.helpers.arrayElement(['me', 'team'] as const),
      similarity_threshold: faker.number.float({ min: 0.1, max: 0.9, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(['active', 'inactive', 'processing']),
      tenant_id: faker.string.uuid(),
      vector_similarity_weight: faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      embd_id: faker.string.uuid(),
      nickname: faker.person.firstName(),
      avatar: null,
      parser_config: {
        chunk_token_num: faker.number.int({ min: 100, max: 1000 }),
        delimiter: faker.helpers.arrayElement([';', ',', '.', '!', '?']),
        html4excel: faker.datatype.boolean(),
        layout_recognize: faker.datatype.boolean(),
        from_page: faker.number.int({ min: 1, max: 10 }),
        to_page: faker.number.int({ min: 10, max: 100 }),
        auto_keywords: faker.number.int({ min: 1, max: 10 }),
        auto_questions: faker.number.int({ min: 1, max: 5 }),
        pages: Array.from(
          { length: faker.number.int({ min: 1, max: 3 }) },
          () =>
            [faker.number.int({ min: 1, max: 50 }), faker.number.int({ min: 51, max: 100 })] as [
              number,
              number,
            ],
        ),
        raptor: {
          use_raptor: faker.datatype.boolean(),
        },
        tag_kb_ids: Array.from({ length: faker.number.int({ min: 0, max: 3 }) }, () =>
          faker.string.uuid(),
        ),
        topn_tags: faker.number.int({ min: 1, max: 10 }),
      },
    }
  })
}

// 缓存生成的数据，避免每次请求都重新生成
const mockData = generateMockKbData()

export const kbHandlers = [
  http.get('/kb-api/datasets', ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('page_size') || '10')

    // 参数验证
    if (page < 1) {
      return HttpResponse.json({ error: 'Page must be greater than 0' }, { status: 400 })
    }

    // 计算分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize

    // 处理超出范围的情况
    if (startIndex >= mockData.length) {
      return HttpResponse.json({
        data: [],
      })
    }

    const paginatedData = mockData.slice(startIndex, endIndex)

    return HttpResponse.json({
      data: paginatedData,
    })
  }),
]
