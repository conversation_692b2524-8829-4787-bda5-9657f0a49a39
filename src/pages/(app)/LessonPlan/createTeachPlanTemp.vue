<template>
  <a-spin :spinning="spinning">
  <div class="page">
    <!-- 顶部返回 -->
    <div class="page-header">
      <div class="left">
        <div @click="handleBack" class="back-btn">
          <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg" />
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="center">教案模板生成</div>
      <div class="right"></div>
    </div>

    <div class="page-content">
      <!-- 主要内容区域 -->
      <div class="main-content">

        <div class="chat-window">
          <div class="avatar">
            <img src="@/assets/image/avatar.svg" alt="机器人图标" class="robot-icon">
          </div>

          <img v-if="loading" src="@/assets/image/3dots.svg" class="robot-icon">

          <div class="bubble" v-else>
            <div class="subtitle">根据课程-授课方向返回教案模板</div>

              <!-- <pre ref="outlineRef" v-if="outlineCreating">{{ outline }}</pre>
              <div class="chat-content" v-else>
                <OutlineTimeline :items="timelineItems"/>
              </div> -->

            <!-- <div class="chat-content" >
              {{ timelineItems }}
              <OutlineTimeline :items="timelineItems" />
            </div> -->
            <div class="chat-content"  v-html="parseMarkdown(timelineItems)"></div>
          </div>

        </div>

        <!-- 保存按钮 -->
        <button class="save-btn" @click="saveTemp()">保存</button>

        <!-- 底部输入区域 -->
        <div class="input-wrapper">
          <input 
            type="text" 
            placeholder="请输入教案模板主题，如：物理"
            v-model="keyword"
            class="input-field"
            @enter="createTeachPlanTemp()"
          >

          <div class="input-actions">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                课程章节
                <img style="width: 0.8vw;height: 0.8vw;" src="@/assets/image/ArrowDown.svg" />
              </span>
              <template #dropdown>
                <chapter-selector :tree-data="treeData" @update:checked="handleCheckedUpdate"
                  ref="chapterSelectorRef" />
              </template>
            </el-dropdown>
            <span class="attach">
              <a-upload :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                  <img style="width: 1vw;height: 1vw;" src="@/assets/image/clip.svg" />
              </a-upload>
            </span>
            <span class="send" @click="createTeachPlanTemp()">
              <img style="width: 1.2vw;height: 1.2vw;" src="@/assets/image/plane.svg"/>
            </span>
          </div>
        </div>

      </div>

      
    </div>
  </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { parseMarkdown } from "@/utils/markdownParser";
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import ChapterSelector from '@/components/lessonPlan/ChapterSelector.vue'
import OutlineTimeline from '@/components/lessonPlan/OutLine.vue';
import { saveTeachPlanTemp, getTeachPlanTemp } from '@/services/api/LessonPlan'
import type { UploadProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';

const router = useRouter()
const handleBack = () => {
  router.back()
}

const spinning = ref(false);
const keyword = ref('')
const textareaValue = ref('')
const outline = ref('')
const outlineRef = ref<HTMLElement>()
const loading = ref(false)
const outlineCreating = ref(false)


const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

// const timelineItems = [
//   { label: "主标题", content: "XXXXX" },
//   { label: "第一章", content: "个人概述" },
//   { label: "1.1", content: "工作历程与角色" },
// ];


interface TimelineItem {
  label: string;
  content: string;
  [key: string]: any; // 允许其他可选属性
}

// const timelineItems = ref<TimelineItem[]>([]);
const timelineItems = ref('');


const createTeachPlanTemp = async () => {
  // loading.value = true

  // try {
  //   const res = await getTeachPlanTemp(input)   
  //   timelineItems.value = parseMarkdownToTimelineItems(res.data);
  // } catch (error) {
  //   console.error('模板生成失败:', error)
  // }

  timelineItems.value = ''
  const params = {
    theme:keyword.value,
    document:fileValue.value
  }
  const res = await getTeachPlanTemp(params)
  if (!res.body) {
    message.error('响应体为空');
    return;
  }
  if (!res.ok) {
    message.error(`HTTP请求错误! 状态码: ${res.status}`);
    return;
  }

  const reader: ReadableStreamDefaultReader = res.body.getReader()
  const decoder = new TextDecoder('utf-8')

  const readStream = () => {
    reader.read().then(({ done, value }) => {
      if (done) {
        return
      }

      const chunk = decoder.decode(value, { stream: true })
      timelineItems.value += chunk
      const chatContent = document.querySelector('.chat-content');

      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight;
      }

      readStream()
    })
  }
  readStream()    
}


function parseMarkdownToTimelineItems(markdownText: string): TimelineItem[] {
  const lines: string[] = markdownText.split('\n').filter(line => line.trim() !== '');
  const Items: TimelineItem[] = [];
  lines.forEach(line => {
    // 主标题 (# 开头)
    if (line.startsWith('# ')) {
      const content: string = line.substring(2);
      const mainTitle: string = content.replace(/（.*?）$/, '').trim();
      Items.push({
        label: "主标题",
        content: mainTitle
      });
    }
    // 章节标题 (## 开头)
    else if (line.startsWith('## ')) {
      const content: string = line.substring(3);
      const chapterTitle: string = content.replace(/（.*?）$/, '').trim();
      const chapterNumMatch = content.match(/（(.*?)）/);
      const chapterNum: string = chapterNumMatch ? chapterNumMatch[1] : "";
      Items.push({
        label: chapterNum,
        content: chapterTitle
      });
    }
    // 小节内容 (- 开头)
    else if (line.startsWith('- ')) {
      const content: string = line.substring(2);
      const sectionText: string = content.replace(/；（.*?）$/, '').trim();
      const sectionNumMatch = content.match(/；（(.*?)）/);
      const sectionNum: string = sectionNumMatch ? sectionNumMatch[1] : "";
      Items.push({
        label: sectionNum,
        content: sectionText
      });
    }
  });

  return Items;
}


const saveTemp = async() => {
  const match = timelineItems.value.match(/^# (.+)$/m);
  const title = match ? match[1] : '无标题';
  const content = timelineItems.value

  const params = {
      title: title,
      content: content,
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    message.success('保存成功！')
  } catch (error) {
    message.error('获取PPT列表失败')
  }
}


const treeData = [
  {
    id: 1,
    label: '课程XXX',
  },
  {
    id: 2,
    label: '课程XXX',
  }
]

const checkedKeys = ref<Array<number | string>>([])

const handleCheckedUpdate = (val: Array<number | string>) => {
  checkedKeys.value = val
}


</script>

<style scoped>
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 5vw;
  right: 5vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 6vh;
  background-color: #ffffff;
  box-sizing: border-box;
}


.left,
.right {
  height: 6vh;
  display: flex;
  align-items: center;
}

.center {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}

.back-btn {
  cursor: pointer;
  margin-left: 2vw;
  font-size: 1vw;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.back-btn:hover {
  color: #3f8cff;
}

.page-content {
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: cover;
  box-sizing: border-box;
  flex: 1;
  /* 关键属性 - 填充剩余空间 */
}

.main-content {
  background-color: transparent;
  border-radius: 8px;
  padding: 1vw 0;
  width: 45%;
  margin: 0 auto;
}

.chat-window {
  display: flex;
  flex-direction: row;
  gap: 1vw;
}

.avatar {
  flex-shrink: 0;
  width: 2vw;
  height: 2vw;
  text-align: center;
  line-height: 2vw;
  font-size: 1rem;
  user-select: none;
}

.bubble {
  flex: 1;
  height: 60vh;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1vw;
  border-radius: 0.8rem;
  background-color: #ffffff;
  color: #000;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  font-size: 1rem;
}

.subtitle {
  padding-bottom: 1vw;
  border-bottom: 0.1vw solid rgba(229, 229, 229, 1);
  margin-bottom: 1vw;
}

.chat-content {
  height: 85%;
  overflow: auto;
  scrollbar-width: none;
}


.icon-header {
  text-align: center;
  margin-bottom: 30px;
}

.robot-icon {
  width: 2vw;
  margin-bottom: 10px;
}



.text {
  width: 80px;
  color: #333;
}

input {
  flex: 1;
  border: none;
  border-bottom: 1px solid #eee;
  padding: 5px;
  outline: none;
}

.save-btn {
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  color: white;
  border: none;
  padding: 8px 30px;
  border-radius: 4px;
  cursor: pointer;
  margin: 1vw 0 1vw 3vw;

}


.input-wrapper {
  display: flex;
  background-color: #ffffff;
  padding: 0.8vw;
  border-radius: 0.5vw;
  box-shadow: 1vw;
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  padding: 0 1vw;
  font-size: 1vw;
  border-radius: 0.5vw;
  background-color: #ffffff;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-dropdown-link {
  width: 6vw;
  height: 4vh;
  font-size: 0.8vw;
  font-weight: 500;
  gap: 0.2vw;
  cursor: pointer;
  background-color: white;
  user-select: none;
  border: 0.1vw solid #dcdfe6;
  padding: 0.5vh 0.8vw;
  border-radius: 94px;
  display: inline-flex;
  align-items: center;
}

.attach,
.send {
  cursor: pointer;
}

/* .attach {
  display: inline-flex;
  align-items: center;
} */

.send {
  width: 3.5vw;
  height: 4vh;
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  color: white;
  padding: 0.5vh 0.8vw;
  border-radius: 2vw;
  margin-left: 1vw;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
</style>
