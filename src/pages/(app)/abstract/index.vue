<template>
  <div class="page">
      <div class="toolbar">
        <div class="toolbar-text">
            <span>摘要提取</span>
        </div>
      </div>
      
      <div class="box-container">
        <div class="box-left" style="  flex: 7;">
            <div class="top">
              <el-select v-model="valueSelect" placeholder="摘要长度" style="width: 100px;min-height: 32px;"
                suffixIcon="CaretBottom">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <a-button style="width: 120px;">
                上传附件<PaperClipOutlined />
              </a-button>

            </div>
            <div class="center">
            
            </div>
            <div class="bottom">
              <a-button type="primary" class="addbut" style="padding: 2px;">
                <img src="@/assets/image/abstract/abstract.png"/>
                摘要提取
              </a-button>
            </div>

            
        </div>
        <div class="box-left" style="flex: 3;">

        </div>
      </div>

</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { PaperClipOutlined } from '@ant-design/icons-vue';

const router = useRouter()

const handleBack = () => {
  router.back()
}

const valueSelect = ref(null)
const options = reactive([
  { value: 1, label: '长' },
  { value: 2, label: '中' },
  { value: 3, label: '短' },
])

</script>


<style scoped lang="scss">
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
  background: url(@/assets/image/graph/graph_bg.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px 50px 40px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}


.addbut {
  width: 120px;
  height: 32px;
  border-radius: 150px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 9px 20px 9px 20px;
  gap:10px;
  background: linear-gradient(90deg, rgba(63, 140, 255, 1) 0%, rgba(21, 192, 230, 1) 100%);
}

.box-container {
  width:100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  gap:20px;
}

.box-left {
  flex: 1;
  height: 100%;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 2px 19px; 
  padding: 30px;
  display: flex;
  flex-direction: column;
  
  .top {

  }
  .center {
    flex: 1;
    border-radius: 5px;
    margin: 20px 0;
    border: 1.5px solid rgba(230, 239, 255, 1);
  }
  .bottom {
    display: flex;
    justify-content: right;
  }
}






</style>