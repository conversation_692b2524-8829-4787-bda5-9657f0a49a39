<template>
  <div class="exam">
    <div>
        <Answer :examList="examList"></Answer>
    </div>
    <div>
        <QuestionsIndex :examList="examList" @changeOptions="changeOptions" @submitExam="submitExam"></QuestionsIndex>
    </div>
    <div>
        <Examtime :examList="examList" @submitExam="submitExam"></Examtime>
    </div>
  </div>
</template>

<script lang="ts" setup name="Exam">
import Answer from '@/components/exam/answer.vue'
import Examtime from '@/components/exam/examtime.vue'
import QuestionsIndex from '@/components/exam/questions-index.vue'

import { ref,reactive} from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

console.log(route.query)

const examList = reactive(
    [{
        article: null,//材料文章
        qsTitle: '本文中，作者列举秦国四位先君广招人才的史实，然后得出“客何负于秦哉”的结论，这里采用的论证方法是()',//题目
        choiseTypName: '单选题',//题目标签名称
        choiseTypeCode: 0,	//题目类型
        userAnswerStr: '',	//用户答案
        userAnswer: [],		//用户答案
        answers: '4',//标准答案 对应选项id即可
        explainText: '因为选择D所以选择D',//解释
        isShow: 0,//
        optionsList: [{	//选项
            id: '1',
            questionOption: '归纳归纳法法归纳归纳法法归纳归纳法法归纳归纳法法归纳归纳法法',
            isCorrect: '0',
        }, {
            id: '2',
            questionOption: '演绎法',
            isCorrect: '0',
        }, {
            id: '3',
            questionOption: '类比法',
            isCorrect: '0',
        }, {
            id: '4',
            questionOption: '对比法',
            isCorrect: '1',
        }]
    }, {
        article: null,
        qsTitle: '本文中，作者列举秦国四位先君广招人才的史实，然后得出“客何负于秦哉”的结论，这里采用的论证方法是()',
        choiseTypName: '多选题',
        choiseTypeCode: 1,
        userAnswerStr: '',	//用户答案
        userAnswer: [],		//用户答案
        answers: '1,4',//答案
        explainText: '因为选择D所以选择D',//解释
        isShow: 0,//
        optionsList: [{
            id: '1',
            questionOption: '归纳法1',
        }, {
            id: '2',
            questionOption: '演绎法1',
        }, {
            id: '3',
            questionOption: '类比法1',
        }, {
            id: '4',
            questionOption: '对比法1',
        }]
    }, {
        article: null,
        qsTitle: '本文中，作者列举秦国四位先君广招人才的史实，然后得出“客何负于秦哉”的结论，这里采用的论证方法是归纳法',
        choiseTypName: '判断题',
        choiseTypeCode: 3,
        userAnswerStr: '',	//用户答案
        userAnswer: [],		//用户答案
        answers: '1',//答案
        explainText: '因为选择D所以选择D',//解释
        isShow: 0,//
        optionsList: [{
            id: '1',
            questionOption: '对',
        }, {
            id: '2',
            questionOption: '错',
        }]
    }, {
        article: null,
        qsTitle: '本文中，作者列举秦国四位先君广招人才的史实，然后得出“客何负于秦哉”的结论，这里采用的论证方法是归纳法？解释一下',
        choiseTypName: '问答题',
        choiseTypeCode: 4,
        userAnswerStr: '',	//用户答案
        userAnswer: [],		//用户答案
        answers: '',//答案
        explainText: '因为选择D所以选择D',//解释
        isShow: 0,//
        optionsList: []
    }, {
        article: null,
        qsTitle: '本文中，作者列举秦国四位先君广招（）的史实，然后得出（）的结论，这里采用的论证方法是（）',
        choiseTypName: '填空题',
        choiseTypeCode: 6,
        keyWords: '1,2,3', //关键字
        userAnswerStr: '',	//用户答案
        userAnswer: [],		//用户答案
        answers: '1',//答案
        explainText: '因为选择D所以选择D',//解释
        isShow: 0,//
        optionsList: []
    }]
)

const isComplete = ref(true)

//修改试题数据，用户选择/作答完更新
function changeOptions(options: { examIndex: number; answerData: any }){
    examList[options.examIndex] = options.answerData;
    console.log(examList,'父组件更新试题')
    //判断是否打完题目 true未打完 ，false 打完
    isComplete.value = examList.some(item=> item.userAnswer.length == 0 || item.userAnswer[0] == '')
}

// 交卷
function submitExam(){
    if(isComplete.value){
        alert('还有题目未完成')
        return
    }
    
}

</script>
<style lang="scss" scoped>
.exam{
    display: flex;
    justify-content: center;
    background-color: rgb(242, 243, 245);
    height: 100vh;
    width: 100vw;
    padding: 40px 0;
}
.exam>div:first-child{
    width: 200px;
}
.exam>div:nth-child(2){
    width: 800px;
    margin: 0 20px;
}
.exam>div:last-child{
    width: 210px;
}

</style>