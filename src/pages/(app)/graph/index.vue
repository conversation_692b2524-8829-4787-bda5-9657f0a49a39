<template>
  <div class="page">
      <div class="toolbar">
        <div class="toolbar-text">
            <span>知识图谱</span>
        </div>
        <div class="toolbar-buttons">
            <a-button type="primary" ghost style="width: 100px;">网状图</a-button>
            <a-button type="primary" ghost style="width: 100px;">树状图</a-button>
            <a-button type="text" class="addbut">保存</a-button>
        </div>
      </div>
      
      <div class="box-container">
        <div class="box">
            
        </div>
        <div class="box" style="background-color: black;">
            <div class="graph-wrapper" :class="{ fullscreen: isenlarge }">
            <!-- 控制面板，包含全屏切换按钮和2D/3D切换按钮 -->
            <div class="control-panel">
                <span class="enlarge-button" :class="{'enlarge-button_full_screen':isenlarge}" @click="toggleEnlarge">
                <el-icon><Rank /></el-icon>
                </span>
                <span class="control-button" :class="{'control-button-fullscreen': isenlarge}" @click="toggle2D3D">
                {{ is3D ? '2D' : '3D' }}
                </span>
            </div>
            <!-- 根据 is3D 状态切换显示 2D 或 3D 组件 -->
            <Graph2D v-if="!is3D && staticNodes.length != 0 && staticLinks.length !=0" :nodes="staticNodes" :links="staticLinks" @deleteNode="handleDeleteNode" :getGraphList="getGraphList"/>
            <Graph3D v-else :nodes="staticNodes" :links="staticLinks" :getGraphList="getGraphList"/>
            </div>
        </div>
      </div>

      <div class="toolbar-buttons">
            <a-button type="primary" ghost style="width: 100px;">
              附件<PaperClipOutlined />
            </a-button>
            <a-button type="text" class="addbut" style="padding: 2px;">生成知识图谱</a-button>
      </div>  

</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { graphList } from "@/services/api/graph";
import Graph2D from '@/components/graph/Graph2D.vue';
import Graph3D from '@/components/graph/Graph3D.vue';
import { PaperClipOutlined } from '@ant-design/icons-vue';

const router = useRouter()

const handleBack = () => {
  router.back()
}

// 控制是否全屏显示的状态
const isenlarge = ref(false);
// 控制是否显示3D渲染的状态
const is3D = ref(false);

// 存储从 API 获取的节点和链接数据
const staticNodes = ref<any[]>([]);
const staticLinks = ref<any[]>([]);

// 获取知识图谱数据列表
async function getGraphList() {
    try {
        const res: any = await graphList(); // 调用 API
        console.log('获取知识图谱列表成功', res);
        if (res && res.nodes && res.edges) {
        console.log('Nodes:', res.nodes);
        console.log('Edges:', res.edges);
        staticNodes.value = res.nodes;
        staticLinks.value = res.edges.map((edge: any) => ({
            ...edge,
            source: edge.source.toString(), // 确保 source 是字符串
            target: edge.target.toString()  // 确保 target 是字符串
        }));
        // 打印转换后的数据进行调试
        console.log('转换后的 staticNodes:', staticNodes.value);
        console.log('转换后的 staticLinks:', staticLinks.value);
        }
    } catch (error) {
        console.error('获取知识图谱列表失败:', error);
    }
}

// 切换全屏显示
const toggleEnlarge = () => {
    isenlarge.value = !isenlarge.value;
};

// 切换2D/3D显示
const toggle2D3D = () => {
    is3D.value = !is3D.value;
    console.log('切换到 3D:', is3D.value); // 打印状态，用于调试
};

// 处理删除节点事件
const handleDeleteNode = async ({ nodes, links }: { nodes: any[], links: any[] }) => {
    // 更新本地数据
    staticNodes.value = nodes;
    staticLinks.value = links;
};

//定义增加节点事件
provide('refreshGraphData', getGraphList);

onMounted(async () => {
    console.log("组件挂载完成");
    await getGraphList(); // 等待数据加载完成
});

</script>


<style scoped lang="scss">
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
  background: url(@/assets/image/graph/graph_bg.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.toolbar-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  .addbut {
    width: 100px;
    color: #fff;
    background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
  }
}


.box-container {
  width:100%;
  height: 750px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  gap:20px;
}

.box {
  flex: 1;
  height: 100%;
  border-radius: 0.2vw;
  background-color: white;
}

/* 图形包装器样式 */
.graph-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 确保全屏模式下的尺寸正确 */
.fullscreen {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    /* z-index: 10000; */
}

/* 控制面板样式 */
.control-panel {
    position: absolute;
    top: 30px;
    left: 10px;
    z-index: 10001; /* 确保控制面板始终在最上层 */
}

/* 放大按钮样式 */
.enlarge-button {
    color: #bbb;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    /* padding: 5px; */
    border-radius: 4px;
    display: inline-block;
}

/* 全屏模式下的放大按钮样式 */
.enlarge-button_full_screen {
    color: #fff;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    padding: 5px;
    border-radius: 4px;
    display: inline-block;
    margin-top: 8vh;
    margin-right: 1vw;
}

/* 控制按钮样式 */
.control-button {
    width: 3rem;
    height: 3rem;
    text-align: center;
    line-height: 2rem;
    color: #bbb;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 4px;
    display: inline-block;
    margin-left: 10px;
}

/* 全屏模式下的控制按钮样式 */
.control-button-fullscreen {
    color: #fff;
    /* margin-top: 8vh; */
    /* margin-right: 20vw; */
}
</style>