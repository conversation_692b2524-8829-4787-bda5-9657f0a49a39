<script lang="ts">
  import { TabsRoot, TabsContent } from 'reka-ui'

  import type { TableItem as KbFile } from '@/components/kb/files/table/KbFileTable.vue'
  import type { Props as KbSettings } from '@/components/kb/settings/KbSettingsPage.vue'

  type KbDetail = {
    name: string
    files: KbFile[]
    settings: KbSettings['defaultValues']
  }

  const fakeKbDetail: KbDetail = {
    name: '机器学习知识库',
    files: [
      {
        id: '1',
        name: '机器学习导论.pdf',
        chunkNum: 24,
        createTime: new Date('2024-01-15'),
        chunkMethod: 'text-splitter',
        chunkStatus: 'complete',
      },
      {
        id: '2',
        name: '深度学习基础.docx',
        chunkNum: 18,
        createTime: new Date('2024-01-16'),
        chunkMethod: 'text-splitter',
        chunkStatus: 'running',
      },
    ],
    settings: {
      name: '机器学习知识库',
      description: '包含机器学习和深度学习相关资料',
      courses: ['人工智能导论', '机器学习'],
      chunkMethod: 'naive',
      keywordNum: 1,
      questionNum: 1,
      separator: ';,@!?.',
    },
  }
</script>

<script setup lang="ts">
  const searchParams = useUrlSearchParams<Record<string, string>>('history')
</script>

<template>
  <TabsRoot
    class="h-full overflow-y-auto [--page-px:40px]"
    default-value="files"
    v-model="searchParams.tab"
    :unmount-on-hide="false"
  >
    <div class="flex items-center space-x-5 px-(--page-px) pt-22.5">
      <div class="text-foreground-2 text-xl font-bold">
        {{ fakeKbDetail.name }}
      </div>

      <KbTabsList />
    </div>

    <TabsContent value="files">
      <KbFilesPage />
    </TabsContent>

    <TabsContent value="settings">
      <KbSettingsPage :default-values="fakeKbDetail.settings" />
    </TabsContent>
  </TabsRoot>
</template>
