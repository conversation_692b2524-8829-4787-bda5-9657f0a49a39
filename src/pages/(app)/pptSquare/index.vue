<template>
  <div class="page">
    <div class="page-header">
      <div @click="handleBack" class="back-btn">
        <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
        <span class="back-text">返回</span>
      </div>
    </div>
    <div class="page-content">
        <h1 class="name">PPT广场</h1>
        <p class="desc">
          介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资
        </p>
        <button  class="primary-btn" @click="creatPPT">
            <img style="width: 22px;" src="@/assets/image/pptSquare/svg.svg"/>
            <span>生成PPT</span>
        </button>
    </div>
    <div class="page-cards">
        <div class="ppt-card">
            <div class="image-wrapper">
                <img src="@/assets/image/pptSquare/png1.png"/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">PPT管理</h2>
                <p class="subtitle">PowerPoint Management</p>
                <button class="enter-btn" @click="interPPT">立即进入</button>
            </div>
        </div> 
        <div class="ppt-card">
            <div class="image-wrapper">
                <img src="@/assets/image/pptSquare/png2.png"/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">模板管理</h2>
                <p class="subtitle">Teaching Plan Management</p>
                <button class="enter-btn" @click="interModule">立即进入</button>
            </div>
        </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function creatPPT() {
  window.open(`${import.meta.env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage`, '_blank');
};
function interPPT() {
  router.push('/pptSquare/pptManage')
};
function interModule() {

};
function handleBack() {
  router.push('/aiSpace')
}

</script>

<style scoped lang="scss">

.page {
  width: 100%;
  min-width: 800px;
  min-height: 100vh;
  background: url(@/assets/image/pptSquare/bg.png) no-repeat center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;

  .page-header{
    width: 100%;
    padding-left: 260px;

    .back-btn {
      width: 60px;
      cursor: pointer;
      padding-top: 30px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .page-content {
    width: 100%;
    padding: 200px 260px 70px 260px;

    .name {
      font-size: 40px;
      margin: 0 0 30px 0;
      font-weight: 700;
    }

    .desc {
      width: 762px;
      height: 120px;
      font-size: 20px;
      font-weight: 500;
      line-height: 40px;
      margin: 0 0 60px 0;
      color: #333;
    }

    .primary-btn {
      width: 154px;
      height: 52px;
      display: flex;
      align-items: center;
      gap: 14px;
      cursor: pointer;
      border: none;
      background:  linear-gradient(135.84deg, rgba(140, 246, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
      color: white;
      padding: 20px;
      border-radius: 30px;
      font-size: 20px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 14px;
    }

    .primary-btn:hover {
      opacity: 0.8;
    }
  }

  .page-cards {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    gap: 52px;
    padding: 0 260px;

    .ppt-card {
      width: 673px;
      height:293px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-color: #fff;
      border-radius: 10px;;
      box-sizing: border-box;
      padding: 30px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    }

    .image-wrapper img {
      min-width: 250px;
    }

    .text-wrapper {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;

      .title {
        font-size: 24px;
        font-weight: 700;
        background-image: linear-gradient(90deg, rgb(0, 102, 255) 0%, rgba(27, 148, 255) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
      }

      .subtitle {
        min-width: 271px;
        font-size: 20px;
        font-weight: 400;
        background-image:  linear-gradient(90deg, rgba(194, 194, 194, 0.3) 0%, rgba(27, 148, 255, 0.3) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 50px;
      }

      .enter-btn {
        width: 90px;
        height: 32px;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 14px;
        color: #1677ff;
        border: 1px solid #1677ff;
        border-radius: 100px;
        background: transparent;
        cursor: pointer;
        transition: all 0.3s;
      }

      .enter-btn:hover {
        background: #1677ff;
        color: white;
      }
    }
  }
}
</style>
