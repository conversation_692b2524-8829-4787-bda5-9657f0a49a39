<template>
    <div class="containerq">
        <div class="flex top">
            <div class="back-btn">
                <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
                <span style="font-size: 14px;line-height: 14px;">返回</span>
            </div>
            <div class="title flex items-center justify-center">我的题库</div>
        </div>

        <div class="quetion">
            <div class="num">共6道试题</div>
            <div class="search">
                <div class="flex items-center gap-5">
                    <a-select v-model:value="value1" style="width: 170px;" size="small" :options="options1"
                        placeholder="选择课程"></a-select>
                    <a-select v-model:value="value1" style="width: 170px;" size="small" :options="options1"
                        placeholder="归属人"></a-select>
                    <a-date-picker show-time placeholder="创建时间" style="width: 237px;" size="small" @change="onChange"
                        @ok="onOk" />
                    <a-input placeholder="输入关键字" size="small" style="width: 258px;margin-left: 50px;">
                        <template #suffix>
                            <SearchOutlined style="color: #C4C4C4;" />
                        </template>
                    </a-input>
                </div>
                <div class="flex align-center" style="margin-top: 17px;">
                    <div style="width: 42px;">题型：</div>
                    <div>
                        <a-radio-group v-model:value="quetionType">
                            <a-radio :value="1">选择题</a-radio>
                            <a-radio :value="2">多选题</a-radio>
                            <a-radio :value="3">填空题</a-radio>
                            <a-radio :value="4">判断题</a-radio>
                            <a-radio :value="5">主观题</a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </div>
            <!-- 全选 -->
            <div class="allselect flex">
                <a-checkbox v-model:checked="checkedAll" style="margin-left: 10px;">全选</a-checkbox>
                <a-button type="text" size="small" style="border: 1px solid #3F8CFF;color: #3F8CFF;margin-left: 20px;">收起题目详情</a-button>
            </div>
        </div>


        <div class="footer">
            已选
        </div>
    </div>
</template>
<script lang="ts" setup name="List">
import { reactive, onMounted, ref, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router'

import { parseMarkdown } from '@/utils/markdownParser'; // 引入工具函数
import { questionList, deleteQuestions, deleteQuestionsBatch, excelAddQue } from '@/services/api/question'
import { openMessage, openMessageBox, getEn, levelTransform, questionTypeData } from "@/utils/util";
import type { ComponentSize } from 'element-plus'
import { usePagination } from '@/hooks/usePagination';

import type { Dayjs } from 'dayjs';


import type { SelectProps } from 'ant-design-vue';
const value1 = ref();
const options1 = ref<SelectProps['options']>([
    {
        value: '1',
        label: '课程1',
    },
    {
        value: '2',
        label: '课程2',
    },
    {
        value: '3',
        label: '课程3',
    },
]);
const onChange = (value: Dayjs, dateString: string) => {
    console.log('Selected Time: ', value);
    console.log('Formatted Selected Time: ', dateString);
};

const onOk = (value: Dayjs) => {
    console.log('onOk: ', value);
};

const quetionType = ref(1)
const checkedAll = ref(false)

</script>
<style lang="scss" scoped>
@import url('@/assets/css/questioncss/style.css');
@import url('@/assets/css/questioncss/questionList.css');
</style>