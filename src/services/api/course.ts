import http from "@/services/http";

//登录
export const courseLogin = (params: any) => {
    return http({
        url: `api/v1/cas/login/`,
        method: "post",
        data: params
    })
}

//添加课程
export const courseAdd = (params: any) => {
    return http({
        url: `api/v1/course/`,
        headers: {
            "Content-Type": "multipart/form-data; boundary=--------------------------043734228796589759250117",
        },
        method: "post",
        data: params
    })
}
//获取课程列表
export const courseListapi = (params: any) => {
    return http({
        url: `api/v1/course/`,
        method: "get",
        params: params
    })
}