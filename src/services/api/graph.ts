import http from "@/services/http";

//获取所有图谱数据
export const graphList = () => {
    return http({
        url: 'v1/api/knowledgegraph/get_all_data/',
        method: "get",
    })
}

//新增节点
export const addGraphNode = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/nodes/',
        method: "post",
        data: params
    })
}

//删除节点
export const deleteGraphNode = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/nodes/',
        method: "post",
        data: params
    })
}

//修改节点
export const updateGraphNode = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/nodes/',
        method: "post",
        data: params
    })
}

//新增连线
export const addGraphEdge = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/edges/',
        method: "post",
        data: params
    })
}

//删除连线
export const deleteGraphEdge = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/edges/',
        method: "post",
        data: params
    })
}

//修改连线
export const updateGraphEdge = (params: any) => {
    return http({
        url: 'v1/api/knowledgegraph/edges/',
        method: "post",
        data: params
    })
}