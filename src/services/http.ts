import axios from "axios";
import { ResultEnum } from "@/enums/ResultEnum";
import { getAccessToken } from "@/utils/auth";
import {openMessage} from "@/utils/util";

// console.log(import.meta.env,'ss');

const http = axios.create({
    baseURL: '/api',//import.meta.env.VITE_APP_API_URL, //// 从环境变量中获取API基础URL
    timeout: 100000,
    headers: {
        "Content-Type": "application/json; charset=utf-8",
    },
});

// 请求拦截器
http.interceptors.request.use(
    (config) => {
        const accessToken = getAccessToken();
        // 如果 Authorization 设置为 no-auth，则不携带 Token，用于登录、刷新 Token 等接口
        if (config.headers.Authorization !== "no-auth" && accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
        } else {
            delete config.headers.Authorization;
        }
        return config;
    },
    (error) => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    (response) => {
        // 如果响应是二进制流，则直接返回，用于下载文件、Excel 导出等
        if (response.config.responseType === "blob") { 
            return response;
        }
        const { code, data, message } = response.data;
        if (code == ResultEnum.SUCCESS) {
            // openMessage(message || "操作成功", "success");
            return response.data;
        }
        
        console.log(message || "接口格式错误");
        return Promise.reject(new Error(message || "Error"));
    },
    (error) => {
        console.error("request error", error); // for debug
        // 非 2xx 状态码处理 401、403、500 等
        const { config, response } = error;
        // 对响应错误做些什么
        if (response) {
            const { code, msg } = response.data;
            console.log(msg , code , "系统出错");
            openMessage("系统出错", "error");
            return Promise.reject(new Error(msg || "Error"));
        }
        return Promise.reject(error);
    }
);

export default http;