import type { ShallowRef } from 'vue'

type KbId = string

export const kbFilesContextInjectionKey = Symbol('kb') as InjectionKey<{
  selectedKbs: Readonly<ShallowRef<KbId[]>>
  setSelectedKbs(value: KbId[]): void

  rawFiles: Readonly<ShallowRef<API.Kb.File[]>>
}>

export function injectKbFilesContext() {
  const context = inject(kbFilesContextInjectionKey)
  if (!context) {
    throw new Error('kbContext is not provided')
  }
  return context
}
